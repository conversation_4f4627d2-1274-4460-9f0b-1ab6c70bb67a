# Smart Eye API - C4 Architecture Documentation

## Level 1: System Context Diagram

```mermaid
C4Context
    title System Context Diagram - Smart Eye IoT Platform

    Person(operators, "Field Operators", "Monitor tanks, pumps, and equipment")
    Person(admins, "System Administrators", "Manage companies, sites, and users")
    Person(managers, "Site Managers", "View reports and analytics")

    System(smart_eye, "Smart Eye API", "IoT monitoring and management platform for industrial equipment")

    System_Ext(tank_devices, "Tank Monitoring Devices", "ATG controllers (MTC, TLS, HYD)")
    System_Ext(pump_devices, "Smart Pump Controllers", "Fuel dispensing systems")
    System_Ext(gen_devices, "Generator Monitors", "Power generation equipment")
    System_Ext(solar_devices, "Solar Panel Monitors", "Renewable energy systems")
    System_Ext(mobile_app, "Mobile Applications", "iOS/Android apps for field operations")
    System_Ext(web_dashboard, "Web Dashboard", "Browser-based management interface")
    System_Ext(email_service, "Email Service", "Notification delivery system")
    System_Ext(sms_service, "SMS Service", "Alert messaging system")

    Rel(operators, mobile_app, "Uses", "HTTPS")
    Rel(admins, web_dashboard, "Uses", "HTTPS")
    Rel(managers, web_dashboard, "Uses", "HTTPS")

    Rel(mobile_app, smart_eye, "API calls", "HTTPS/REST")
    Rel(web_dashboard, smart_eye, "API calls", "HTTPS/REST")

    Rel(tank_devices, smart_eye, "Sends telemetry", "HTTP/MQTT")
    Rel(pump_devices, smart_eye, "Sends transactions", "HTTP")
    Rel(gen_devices, smart_eye, "Sends runtime data", "HTTP")
    Rel(solar_devices, smart_eye, "Sends energy data", "HTTP")

    Rel(smart_eye, tank_devices, "Config updates", "HTTP")
    Rel(smart_eye, pump_devices, "Price changes", "HTTP")
    Rel(smart_eye, email_service, "Sends alerts", "SMTP")
    Rel(smart_eye, sms_service, "Sends alerts", "HTTP")

    UpdateElementStyle(smart_eye, $fontColor="white", $bgColor="blue", $borderColor="navy")
```

## Level 2: Container Diagram

```mermaid
C4Container
    title Container Diagram - Smart Eye API Platform

    Person(users, "Users", "Operators, Admins, Managers")
    System_Ext(iot_devices, "IoT Devices", "Tanks, Pumps, Generators")
    System_Ext(external_services, "External Services", "Email, SMS, Push Notifications")

    System_Boundary(smart_eye_system, "Smart Eye Platform") {
        Container(api_gateway, "API Gateway", "Nginx/Kong", "Routes requests, handles SSL, rate limiting")
        Container(web_api, "Web API", "Django 2.2.15 + DRF", "REST API for all business operations")
        Container(auth_service, "Authentication Service", "Django + JWT", "User authentication and authorization")
        Container(task_workers, "Background Workers", "Celery", "Processes alarms, reports, notifications")
        Container(scheduler, "Task Scheduler", "Celery Beat", "Schedules periodic tasks and reports")

        ContainerDb(primary_db, "Primary Database", "MySQL 8.0", "Stores all application data")
        ContainerDb(cache_db, "Cache & Message Broker", "Redis 6.0", "Caching and task queue")
        ContainerDb(file_storage, "File Storage", "Local/S3", "Calibration charts, reports, images")

        Container(monitoring, "Application Monitoring", "Sentry", "Error tracking and performance monitoring")
    }

    Rel(users, api_gateway, "Uses", "HTTPS")
    Rel(iot_devices, api_gateway, "Sends data", "HTTP/MQTT")

    Rel(api_gateway, web_api, "Routes to", "HTTP")
    Rel(api_gateway, auth_service, "Auth requests", "HTTP")

    Rel(web_api, auth_service, "Validates tokens", "HTTP")
    Rel(web_api, primary_db, "Reads/Writes", "MySQL Protocol")
    Rel(web_api, cache_db, "Caches data", "Redis Protocol")
    Rel(web_api, task_workers, "Queues tasks", "Redis")
    Rel(web_api, file_storage, "Stores files", "File System/S3")

    Rel(task_workers, primary_db, "Reads/Writes", "MySQL Protocol")
    Rel(task_workers, cache_db, "Gets tasks", "Redis Protocol")
    Rel(task_workers, external_services, "Sends notifications", "SMTP/HTTP")

    Rel(scheduler, task_workers, "Schedules tasks", "Redis")

    Rel(web_api, monitoring, "Sends errors", "HTTPS")
    Rel(task_workers, monitoring, "Sends errors", "HTTPS")

    UpdateElementStyle(web_api, $fontColor="white", $bgColor="blue", $borderColor="navy")
    UpdateElementStyle(task_workers, $fontColor="white", $bgColor="green", $borderColor="darkgreen")
```

## Level 3: Component Diagram - Web API Container

```mermaid
C4Component
    title Component Diagram - Smart Eye Web API

    System_Ext(iot_devices, "IoT Devices")
    System_Ext(client_apps, "Client Applications")
    Container_Ext(auth_service, "Authentication Service")
    Container_Ext(task_workers, "Background Workers")
    ContainerDb_Ext(primary_db, "Primary Database")
    ContainerDb_Ext(cache_db, "Redis Cache")

    Container_Boundary(web_api, "Web API Container") {
        Component(api_router, "API Router", "Django URLs", "Routes requests to appropriate modules")
        Component(middleware, "Middleware Stack", "Django Middleware", "CORS, Auth, Logging, Rate Limiting")

        Component(auth_module, "Authentication Module", "Django + JWT", "Login, logout, password reset")
        Component(user_mgmt, "User Management", "Django Models/Views", "User CRUD, roles, permissions")
        Component(company_mgmt, "Company Management", "Django Models/Views", "Multi-tenant company operations")
        Component(site_mgmt, "Site Management", "Django Models/Views", "Site configuration and monitoring")

        Component(tank_monitoring, "Tank Monitoring", "Django Models/Views", "Tank data, alarms, calibration")
        Component(pump_mgmt, "Smart Pump Management", "Django Models/Views", "Pump transactions, pricing")
        Component(device_mgmt, "Device Management", "Django Models/Views", "IoT device registration and config")
        Component(generator_mgmt, "Generator Monitoring", "Django Models/Views", "Generator runtime tracking")
        Component(solar_mgmt, "Solar Monitoring", "Django Models/Views", "Solar panel performance")

        Component(reporting, "Reporting Engine", "Django Views + Utils", "Generate consumption, sales reports")
        Component(analytics, "Analytics Engine", "Django Views + Utils", "Data analysis and insights")
        Component(notifications, "Notification Manager", "Django Models/Views", "Alert configuration and delivery")

        Component(public_api, "Public API", "Django Views", "External integration endpoints")
        Component(file_handler, "File Handler", "Django Utils", "Calibration charts, document management")
        Component(data_validator, "Data Validator", "Django Utils", "Input validation and sanitization")
    }

    Rel(client_apps, api_router, "HTTP Requests", "HTTPS/REST")
    Rel(iot_devices, api_router, "Telemetry Data", "HTTP/JSON")

    Rel(api_router, middleware, "Routes through")
    Rel(middleware, auth_module, "Auth requests")
    Rel(middleware, user_mgmt, "User operations")
    Rel(middleware, company_mgmt, "Company operations")
    Rel(middleware, site_mgmt, "Site operations")
    Rel(middleware, tank_monitoring, "Tank operations")
    Rel(middleware, pump_mgmt, "Pump operations")
    Rel(middleware, device_mgmt, "Device operations")
    Rel(middleware, generator_mgmt, "Generator operations")
    Rel(middleware, solar_mgmt, "Solar operations")
    Rel(middleware, reporting, "Report requests")
    Rel(middleware, analytics, "Analytics requests")
    Rel(middleware, notifications, "Notification requests")
    Rel(middleware, public_api, "Public API requests")

    Rel(auth_module, auth_service, "Validates tokens", "HTTP")

    Rel(tank_monitoring, data_validator, "Validates input")
    Rel(pump_mgmt, data_validator, "Validates input")
    Rel(device_mgmt, data_validator, "Validates input")

    Rel(tank_monitoring, file_handler, "Calibration charts")
    Rel(reporting, file_handler, "Report files")

    Rel(tank_monitoring, task_workers, "Queue alarms", "Redis")
    Rel(pump_mgmt, task_workers, "Queue price updates", "Redis")
    Rel(reporting, task_workers, "Queue report generation", "Redis")
    Rel(notifications, task_workers, "Queue notifications", "Redis")

    Rel(user_mgmt, primary_db, "User data", "MySQL")
    Rel(company_mgmt, primary_db, "Company data", "MySQL")
    Rel(site_mgmt, primary_db, "Site data", "MySQL")
    Rel(tank_monitoring, primary_db, "Tank data", "MySQL")
    Rel(pump_mgmt, primary_db, "Pump data", "MySQL")
    Rel(device_mgmt, primary_db, "Device data", "MySQL")
    Rel(generator_mgmt, primary_db, "Generator data", "MySQL")
    Rel(solar_mgmt, primary_db, "Solar data", "MySQL")
    Rel(reporting, primary_db, "Report data", "MySQL")
    Rel(analytics, primary_db, "Analytics data", "MySQL")
    Rel(notifications, primary_db, "Notification data", "MySQL")

    Rel(tank_monitoring, cache_db, "Cache latest readings", "Redis")
    Rel(pump_mgmt, cache_db, "Cache transactions", "Redis")
    Rel(analytics, cache_db, "Cache calculations", "Redis")

    UpdateElementStyle(tank_monitoring, $fontColor="white", $bgColor="orange", $borderColor="darkorange")
    UpdateElementStyle(pump_mgmt, $fontColor="white", $bgColor="purple", $borderColor="darkpurple")
    UpdateElementStyle(device_mgmt, $fontColor="white", $bgColor="green", $borderColor="darkgreen")
```

## Level 4: Deployment Diagram

```mermaid
C4Deployment
    title Deployment Diagram - Smart Eye Production Environment

    Deployment_Node(cloud_provider, "Cloud Provider", "AWS/Digital Ocean/Azure") {
        Deployment_Node(load_balancer, "Load Balancer", "Nginx/HAProxy") {
            Container(lb_service, "Load Balancer", "Nginx", "SSL termination, load balancing")
        }

        Deployment_Node(app_cluster, "Application Cluster", "Docker Swarm/Kubernetes") {
            Deployment_Node(api_node1, "API Node 1", "Ubuntu 20.04 LTS") {
                Container(api_app1, "Smart Eye API", "Django 2.2.15", "Primary API instance")
                Container(celery_worker1, "Celery Worker", "Python 3.8", "Background task processor")
            }

            Deployment_Node(api_node2, "API Node 2", "Ubuntu 20.04 LTS") {
                Container(api_app2, "Smart Eye API", "Django 2.2.15", "Secondary API instance")
                Container(celery_worker2, "Celery Worker", "Python 3.8", "Background task processor")
            }

            Deployment_Node(scheduler_node, "Scheduler Node", "Ubuntu 20.04 LTS") {
                Container(celery_beat, "Celery Beat", "Python 3.8", "Task scheduler")
                Container(celery_flower, "Celery Flower", "Python 3.8", "Task monitoring")
            }
        }

        Deployment_Node(database_cluster, "Database Cluster", "High Availability") {
            Deployment_Node(db_primary, "Primary DB Server", "Ubuntu 20.04 LTS") {
                ContainerDb(mysql_primary, "MySQL Primary", "MySQL 8.0", "Primary database instance")
            }

            Deployment_Node(db_replica, "Replica DB Server", "Ubuntu 20.04 LTS") {
                ContainerDb(mysql_replica, "MySQL Replica", "MySQL 8.0", "Read replica for reporting")
            }
        }

        Deployment_Node(cache_cluster, "Cache Cluster", "Redis Cluster") {
            Deployment_Node(redis_node1, "Redis Node 1", "Ubuntu 20.04 LTS") {
                ContainerDb(redis_primary, "Redis Primary", "Redis 6.0", "Primary cache and message broker")
            }

            Deployment_Node(redis_node2, "Redis Node 2", "Ubuntu 20.04 LTS") {
                ContainerDb(redis_replica, "Redis Replica", "Redis 6.0", "Cache replica")
            }
        }

        Deployment_Node(storage_service, "File Storage", "Object Storage") {
            ContainerDb(file_storage, "S3/MinIO", "Object Storage", "Files, reports, calibration charts")
        }

        Deployment_Node(monitoring_stack, "Monitoring Stack", "Observability") {
            Container(sentry, "Sentry", "Error Tracking", "Application error monitoring")
            Container(prometheus, "Prometheus", "Metrics", "System and application metrics")
            Container(grafana, "Grafana", "Dashboards", "Monitoring dashboards")
        }
    }

    System_Ext(iot_devices, "IoT Devices", "Field equipment")
    System_Ext(client_apps, "Client Applications", "Web/Mobile apps")
    System_Ext(external_services, "External Services", "Email, SMS providers")

    Rel(iot_devices, lb_service, "Sends telemetry", "HTTPS")
    Rel(client_apps, lb_service, "API requests", "HTTPS")

    Rel(lb_service, api_app1, "Routes requests", "HTTP")
    Rel(lb_service, api_app2, "Routes requests", "HTTP")

    Rel(api_app1, mysql_primary, "Read/Write", "MySQL Protocol")
    Rel(api_app2, mysql_primary, "Read/Write", "MySQL Protocol")
    Rel(api_app1, mysql_replica, "Read only", "MySQL Protocol")
    Rel(api_app2, mysql_replica, "Read only", "MySQL Protocol")

    Rel(mysql_primary, mysql_replica, "Replication", "MySQL Protocol")

    Rel(api_app1, redis_primary, "Cache/Queue", "Redis Protocol")
    Rel(api_app2, redis_primary, "Cache/Queue", "Redis Protocol")
    Rel(celery_worker1, redis_primary, "Get tasks", "Redis Protocol")
    Rel(celery_worker2, redis_primary, "Get tasks", "Redis Protocol")
    Rel(celery_beat, redis_primary, "Schedule tasks", "Redis Protocol")

    Rel(redis_primary, redis_replica, "Replication", "Redis Protocol")

    Rel(api_app1, file_storage, "Store/Retrieve files", "S3 API")
    Rel(api_app2, file_storage, "Store/Retrieve files", "S3 API")

    Rel(celery_worker1, external_services, "Send notifications", "SMTP/HTTP")
    Rel(celery_worker2, external_services, "Send notifications", "SMTP/HTTP")

    Rel(api_app1, sentry, "Error reports", "HTTPS")
    Rel(api_app2, sentry, "Error reports", "HTTPS")
    Rel(celery_worker1, sentry, "Error reports", "HTTPS")
    Rel(celery_worker2, sentry, "Error reports", "HTTPS")

    UpdateElementStyle(api_app1, $fontColor="white", $bgColor="blue", $borderColor="navy")
    UpdateElementStyle(api_app2, $fontColor="white", $bgColor="blue", $borderColor="navy")
    UpdateElementStyle(mysql_primary, $fontColor="white", $bgColor="orange", $borderColor="darkorange")
    UpdateElementStyle(redis_primary, $fontColor="white", $bgColor="red", $borderColor="darkred")
```
## Supporting Diagrams

### Data Flow Diagram - Tank Monitoring

```mermaid
flowchart TD
    A[IoT Tank Device] -->|HTTP POST| B[API Gateway]
    B --> C[Tank Monitoring Module]
    C --> D{Validate Data}
    D -->|Valid| E[Store in AtgPrimaryLog]
    D -->|Invalid| F[Log Error & Reject]
    E --> G[Update LatestAtgLog]
    G --> H[Calculate Volume from Height]
    H --> I{Check Alarm Thresholds}
    I -->|Normal| J[Update Tank Status]
    I -->|Alarm| K[Queue Alarm Task]
    K --> L[Send Email/SMS Alert]
    J --> M[Cache Latest Reading]
    L --> M
    M --> N[Update Dashboard]

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style K fill:#ffebee
    style L fill:#ffebee
```

### Sequence Diagram - Pump Transaction Flow

```mermaid
sequenceDiagram
    participant PD as Pump Device
    participant API as Smart Eye API
    participant DB as Database
    participant CW as Celery Worker
    participant NS as Notification Service

    PD->>API: POST /smartpump/transaction_logger/
    API->>API: Validate transaction data
    API->>DB: Store transaction in TransactionData
    API->>API: Calculate totals and summaries
    API->>CW: Queue price update task (if needed)
    API-->>PD: 200 OK with confirmation

    CW->>DB: Update pump configuration
    CW->>NS: Send price change notification
    NS->>NS: Send email to site managers

    Note over API,DB: Real-time transaction processing
    Note over CW,NS: Asynchronous notifications
```

### Entity Relationship Overview

```mermaid
erDiagram
    COMPANIES ||--o{ SITES : owns
    COMPANIES ||--o{ USERS : employs
    SITES ||--o{ DEVICES : contains
    SITES ||--o{ TANKS : contains
    SITES ||--o{ PUMPS : contains
    DEVICES ||--o{ ATG_PRIMARY_LOG : generates
    TANKS ||--o{ LATEST_ATG_LOG : "has current state"
    PUMPS ||--o{ NOZZLES : contains
    PUMPS ||--o{ TRANSACTION_DATA : generates
    USERS }o--|| ROLES : "assigned to"
    ROLES ||--o{ PERMISSIONS : contains

    COMPANIES {
        int Company_id PK
        string Name
        string Country
        string State
        boolean Active
        boolean smarttank_access
        boolean smartpump_access
    }

    SITES {
        int Site_id PK
        int Company_id FK
        string Name
        string Site_type
        boolean Active
        boolean Communication_status
    }

    TANKS {
        int Tank_id PK
        int Site_id FK
        string Name
        int Capacity
        string Tank_controller
        int Tank_index
        boolean Status
    }

    DEVICES {
        int Device_id PK
        int Site_id FK
        string Device_unique_address
        boolean Active
        datetime last_seen
    }
```

### Microservices Migration Plan

```mermaid
graph TB
    subgraph "Current Monolith"
        M[Smart Eye Django App]
    end

    subgraph "Target Microservices Architecture"
        AG[API Gateway]

        subgraph "Core Services"
            AS[Auth Service]
            US[User Service]
            CS[Company Service]
        end

        subgraph "Domain Services"
            TS[Tank Service]
            PS[Pump Service]
            DS[Device Service]
            GS[Generator Service]
            SS[Solar Service]
        end

        subgraph "Cross-Cutting Services"
            NS[Notification Service]
            RS[Reporting Service]
            ANS[Analytics Service]
        end

        subgraph "Data Layer"
            CDB[(Core DB)]
            TDB[(Tank DB)]
            PDB[(Pump DB)]
            RDB[(Redis Cache)]
        end
    end

    M -.->|Phase 1: Extract| AS
    M -.->|Phase 1: Extract| US
    M -.->|Phase 2: Extract| TS
    M -.->|Phase 2: Extract| PS
    M -.->|Phase 3: Extract| NS
    M -.->|Phase 3: Extract| RS

    AG --> AS
    AG --> US
    AG --> CS
    AG --> TS
    AG --> PS
    AG --> DS
    AG --> GS
    AG --> SS

    AS --> CDB
    US --> CDB
    CS --> CDB
    TS --> TDB
    PS --> PDB

    NS --> RDB
    RS --> RDB
    ANS --> RDB

    style M fill:#ffcdd2
    style AG fill:#e8f5e8
    style AS fill:#fff3e0
    style TS fill:#e3f2fd
    style PS fill:#f3e5f5
```
## Architecture Summary

### Key Architectural Decisions

1. **Multi-Tenant Architecture**: Company-based data isolation with role-based access control
2. **Modular Django Design**: Separate apps for each business domain (tanks, pumps, devices, etc.)
3. **Asynchronous Processing**: Celery for background tasks, alarms, and report generation
4. **Caching Strategy**: Redis for session management, task queuing, and data caching
5. **Real-time Monitoring**: WebSocket support for live dashboard updates
6. **Scalable Storage**: Support for both local and cloud-based file storage
7. **Comprehensive Logging**: Audit trails and error tracking with Sentry integration

### Technology Stack Summary

| Layer | Technology | Purpose |
|-------|------------|---------|
| **API Framework** | Django 2.2.15 + DRF | REST API development |
| **Database** | MySQL 8.0 | Primary data storage |
| **Cache/Queue** | Redis 6.0 | Caching and message broker |
| **Task Processing** | Celery + Celery Beat | Background tasks and scheduling |
| **Authentication** | JWT (SimpleJWT) | Stateless authentication |
| **Documentation** | DRF Spectacular | OpenAPI/Swagger documentation |
| **Monitoring** | Sentry | Error tracking and performance monitoring |
| **File Storage** | Local/S3 | Document and media storage |
| **Load Balancing** | Nginx | Reverse proxy and load balancing |

### Scalability Considerations

1. **Horizontal Scaling**: Multiple API instances behind load balancer
2. **Database Optimization**: Read replicas for reporting workloads
3. **Caching Strategy**: Multi-level caching (Redis + application-level)
4. **Background Processing**: Distributed Celery workers
5. **File Storage**: Cloud-based object storage for scalability
6. **Monitoring**: Comprehensive observability stack

### Security Features

1. **Authentication**: JWT-based stateless authentication
2. **Authorization**: Role-based access control (RBAC)
3. **Data Isolation**: Multi-tenant architecture with company-level isolation
4. **API Security**: Rate limiting, CORS configuration, input validation
5. **Audit Logging**: Comprehensive audit trails for all operations
6. **Secure Communication**: HTTPS/TLS encryption for all communications

### Future Roadmap

1. **Phase 1**: Extract authentication and user management services
2. **Phase 2**: Separate tank and pump monitoring into dedicated services
3. **Phase 3**: Implement API gateway and service mesh
4. **Phase 4**: Containerization and Kubernetes deployment
5. **Phase 5**: Event-driven architecture with message streaming

This architecture supports the current monolithic deployment while providing a clear path toward microservices migration as the platform scales.