# Smart Eye API - Complete C4 Documentation

## 1. System Context Diagram
```mermaid
C4Context
    title System Context Diagram for Smart Eye API
    Enterprise_Boundary(b0, "Smart Eye Ecosystem") {
        System(smart_eye, "Smart Eye API", "Django-based IoT monitoring platform")
        System_Ext(iot_devices, "IoT Devices", "Tanks, pumps, generators")
        System_Ext(mobile_app, "Mobile App", "Operator mobile application")
        System_Ext(web_app, "Web Dashboard", "Admin web interface")
        Rel(iot_devices, smart_eye, "Sends telemetry")
        Rel(smart_eye, iot_devices, "Sends config updates")
        Rel(mobile_app, smart_eye, "API calls")
        Rel(web_app, smart_eye, "API calls")
    }
```

## 2. Container Diagram
```mermaid
C4Container
    title Container Diagram for Smart Eye API
    Enterprise_Boundary(b0, "Smart Eye Ecosystem") {
        Container(smart_eye, "Smart Eye API", "Django 2.2.15", "RESTful API for IoT monitoring")
        ContainerDb(db, "MySQL Database", "MySQL 5.7", "Stores telemetry data, configurations, etc.")
        ContainerQueue(queue, "Celery Task Queue", "Redis", "Handles background tasks")
        Rel(smart_eye, db, "Reads from and writes to", "JDBC")
        Rel(smart_eye, queue, "Sends tasks to", "AMQP")
    }
```

## 3. Component Diagram
```mermaid
C4Component
    title Component Diagram for Smart Eye API
    Container(smart_eye, "Smart Eye API", "Django 2.2.15", "RESTful API for IoT monitoring") {
        Component(api, "API Endpoints", "Django REST Framework", "Handles incoming requests")
        Component(db, "Database", "MySQL 5.7", "Stores telemetry data, configurations, etc.")
        Component(queue, "Task Queue", "Celery with Redis", "Handles background tasks")
        Component(auth, "Authentication", "JWT with SimpleJWT", "Handles user authentication")
        Rel(api, db, "Reads from and writes to", "JDBC")
        Rel(api, queue, "Sends tasks to", "AMQP")
        Rel(api, auth, "Uses for authentication", "HTTP")
    }
```

## 4. Deployment Diagram
```mermaid
C4Deployment
    title Deployment Diagram for Smart Eye API
    Deployment_Node(api_server, "API Server") {
        Container(smart_eye, "Smart Eye API", "Django 2.2.15", "RESTful API for IoT monitoring")
    }
    Deployment_Node(db_server, "Database Server") {
        ContainerDb(db, "MySQL Database", "MySQL 5.7", "Stores telemetry data, configurations, etc.")
    }
    Deployment_Node(queue_server, "Queue Server") {
        ContainerQueue(queue, "Celery Task Queue", "Redis", "Handles background tasks")
    }
    Rel(smart_eye, db, "Reads from and writes to", "JDBC")
    Rel(smart_eye, queue, "Sends tasks to", "AMQP")
```     
C4Container
    title Container Diagram
    System_Boundary(smart_eye, "Smart Eye Platform") {
        Container(web_app, "Web Application", "Django")
        Container(api, "REST API", "DRF")
        Container(celery, "Celery Workers", "Python")
        Container(db, "Database", "MySQL")
        Container(redis, "Redis", "Message broker")
    }
    System_Ext(device, "IoT Device")
    System_Ext(user, "End User")
    Rel(device, api, "HTTP/MQTT")
    Rel(user, web_app, "Uses")
    Rel(web_app, api, "API calls")
    Rel(api, db, "Reads from and writes to")
    Rel(api, celery, "Sends tasks to")
    Rel(celery, redis, "Sends tasks to")
    Rel(redis, celery, "Sends tasks to")
```
C4Component
    title Component Diagram
    Container_Boundary(api, "REST API Service") {
        Component(auth, "Authentication", "JWT")
        Component(tank, "Tank Monitoring")
        Component(pump, "Pump Management")
        Rel(auth, tank, "Authorizes")
        Rel(tank, pump, "Shares data")
    }
    Container(db, "Database")
    Rel(tank, db, "Reads/Writes")

C4Deployment
    title Digital Ocean Deployment
    Deployment_Node(do, "Digital Ocean") {
        Deployment_Node(prod, "Production") {
            Node(portal, "Customer Portal") {
                Container(frontend, "Vue.js")
            }
            Node(smarteye, "SmartEye Service") {
                Container(iot_api, "IoT API")
            }
            Node(db, "Database Server") {
                Container(mysql, "MySQL")
            }
        }
    }
    System_Ext(iot, "IoT Devices")
    Rel(iot, smarteye, "Telemetry")

    classDiagram
    class Tank {
        +get_volume()
    }
    class CalibrationChart {
        +height_volume_map
    }
    Tank --> CalibrationChart

    sequenceDiagram
    participant Device
    participant API
    participant DB
    Device->>API: POST data
    API->>DB: Store readings
flowchart TD
    A[IoT Devices] -->|Telemetry| B(SmartEye Service:8001)
    A -->|Fetch Config| C(Remote Config:8002)
    D[Customer Browser] -->|HTTPS| E(Customer Portal:443)
    E -->|API Calls| B
    B -->|Persist Data| F[(MySQL DB)]
    C -->|Config Data| F
    B <-->|Redis Pub/Sub| C
    E -->|Static Files| G[(S3 Storage)]