<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Eye API - C4 Architecture Documentation</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        h4 {
            color: #34495e;
            margin-top: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        .page-break {
            page-break-before: always;
        }
        @media print {
            body {
                font-size: 12pt;
                line-height: 1.4;
            }
            h1 {
                font-size: 18pt;
            }
            h2 {
                font-size: 16pt;
            }
            h3 {
                font-size: 14pt;
            }
            .mermaid {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            c4: {
                useMaxWidth: true
            }
        });
    </script>

<h1>Smart Eye API - C4 Architecture Documentation</h1>

<strong>Document Version:</strong> 1.0  
<strong>Date:</strong> December 2024  
<strong>Author:</strong> Smart Eye Development Team

<hr>

<h2 class="page-break">Table of Contents</h2>

<ol><li>[Overview](#overview)</li><li>[Level 1: System Context Diagram](#level-1-system-context-diagram)</li><li>[Level 2: Container Diagram](#level-2-container-diagram)</li><li>[Level 3: Component Diagram](#level-3-component-diagram)</li><li>[Level 4: Deployment Diagram](#level-4-deployment-diagram)</li><li>[Supporting Diagrams](#supporting-diagrams)</li><li>[Architecture Summary](#architecture-summary)</li><li>[API Module Structure](#api-module-structure)</li></ol>

<hr>

<h2 class="page-break">Overview</h2>

<p>This document provides a comprehensive architectural view of the Smart Eye API platform using the C4 model. The Smart Eye API is a Django-based IoT monitoring and management platform for industrial equipment including tanks, pumps, generators, and other devices.</p>

<strong>Key Features:</strong>

<ul><li>  Multi-tenant architecture with company-based data isolation</li><li>  Real-time IoT device monitoring and control</li><li>  Comprehensive alarm and notification system</li><li>  Advanced reporting and analytics capabilities</li><li>  Scalable microservices-ready design</li></ul>

<hr>

<h2 class="page-break">Level 1: System Context Diagram</h2>

<p>The system context diagram shows how the Smart Eye API fits into the broader ecosystem of users, devices, and external services.</p>

<div class="mermaid">
C4Context
    title System Context Diagram - Smart Eye IoT Platform

<p>Person(operators, "Field Operators", "Monitor tanks, pumps, and equipment")
    Person(admins, "System Administrators", "Manage companies, sites, and users")
    Person(managers, "Site Managers", "View reports and analytics")</p>

<p>System(smart_eye, "Smart Eye API", "IoT monitoring and management platform for industrial equipment")</p>

<p>System_Ext(tank_devices, "Tank Monitoring Devices", "ATG controllers (MTC, TLS, HYD-2, Sensor)")
    System_Ext(pump_devices, "Smart Pump Controllers", "Fuel dispensing systems with PIC controllers")
    System_Ext(gen_devices, "Generator Monitors", "Power generation equipment tracking")
    System_Ext(solar_devices, "Solar Panel Monitors", "Renewable energy systems")
    System_Ext(counter_devices, "Smart Counters", "Counter machine monitoring")
    System_Ext(flowmeter_devices, "Flowmeters", "Flow measurement devices")
    System_Ext(hydrostatic_devices, "Hydrostatic Probes", "Tank level measurement via pressure")
    System_Ext(mobile_app, "Mobile Applications", "iOS/Android apps for field operations")
    System_Ext(web_dashboard, "Web Dashboard", "Browser-based management interface")
    System_Ext(email_service, "Email Service", "SMTP notification delivery")
    System_Ext(sms_service, "SMS Service", "Twilio-based alert messaging")
    System_Ext(push_service, "Push Notifications", "Firebase FCM for mobile alerts")
    System_Ext(tapnet_system, "TapNet Integration", "External payment and card systems")</p>

<p>Rel(operators, mobile_app, "Uses", "HTTPS")
    Rel(admins, web_dashboard, "Uses", "HTTPS")
    Rel(managers, web_dashboard, "Uses", "HTTPS")</p>

<p>Rel(mobile_app, smart_eye, "API calls", "HTTPS/REST")
    Rel(web_dashboard, smart_eye, "API calls", "HTTPS/REST")</p>

<p>Rel(tank_devices, smart_eye, "Sends telemetry", "HTTP/JSON")
    Rel(pump_devices, smart_eye, "Sends transactions", "HTTP/JSON")
    Rel(gen_devices, smart_eye, "Sends runtime data", "HTTP/JSON")
    Rel(solar_devices, smart_eye, "Sends energy data", "HTTP/JSON")
    Rel(counter_devices, smart_eye, "Sends counter logs", "HTTP/JSON")
    Rel(flowmeter_devices, smart_eye, "Sends flow data", "HTTP/JSON")
    Rel(hydrostatic_devices, smart_eye, "Sends pressure data", "HTTP/JSON")</p>

<p>Rel(smart_eye, tank_devices, "Remote config", "HTTP/JSON")
    Rel(smart_eye, pump_devices, "Price changes & config", "HTTP/JSON")
    Rel(smart_eye, email_service, "Alarm notifications", "SMTP")
    Rel(smart_eye, sms_service, "Critical alerts", "HTTP/Twilio API")
    Rel(smart_eye, push_service, "Mobile notifications", "HTTP/FCM")
    Rel(smart_eye, tapnet_system, "Payment integration", "HTTP/REST")</p>

<p>UpdateElementStyle(smart_eye, $fontColor="white", $bgColor="blue", $borderColor="navy")
</div></p>

<h3>Key External Actors:</h3>

<ul><li>  <strong>Field Operators</strong>: Monitor equipment using mobile applications</li><li>  <strong>System Administrators</strong>: Manage companies, sites, and users via web dashboard</li><li>  <strong>Site Managers</strong>: View reports and analytics through web interface</li></ul>

<h3>External Systems:</h3>

<ul><li>  <strong>IoT Devices</strong>: Various monitoring devices (ATG controllers, pump controllers, generators, etc.)</li><li>  <strong>Client Applications</strong>: Mobile apps and web dashboards</li><li>  <strong>External Services</strong>: Email, SMS, push notifications, payment systems</li></ul>

<hr>

<h2 class="page-break">Level 2: Container Diagram</h2>

<p>The container diagram shows the high-level technology choices and how responsibilities are distributed across containers within the Smart Eye platform.</p>

<div class="mermaid">
C4Container
    title Container Diagram - Smart Eye API Platform

<p>Person(users, "Users", "Operators, Admins, Managers")
    System_Ext(iot_devices, "IoT Devices", "Tanks, Pumps, Generators")
    System_Ext(external_services, "External Services", "Email, SMS, Push Notifications")</p>

<p>System_Boundary(smart_eye_system, "Smart Eye Platform") {
        Container(api_gateway, "API Gateway", "Nginx/Kong", "Routes requests, handles SSL, rate limiting")
        Container(web_api, "Web API", "Django 2.2.15 + DRF", "REST API with 20+ business modules")
        Container(auth_service, "Authentication Service", "Django + SimpleJWT", "JWT-based stateless authentication")
        Container(task_workers, "Background Workers", "Celery 4.2.2", "Alarm processing, report generation")
        Container(scheduler, "Task Scheduler", "Celery Beat", "Cron jobs and periodic maintenance")
        Container(rabbit_services, "Message Services", "RabbitMQ", "Real-time device communication")</p>

<p>ContainerDb(primary_db, "Primary Database", "MySQL with PyMySQL", "All application and telemetry data")
        ContainerDb(cache_db, "Cache & Message Broker", "Redis 3.2.1", "Session cache and task queue")
        ContainerDb(file_storage, "File Storage", "Custom Storage", "Tank charts, maintenance images, reports")</p>

<p>Container(monitoring, "Application Monitoring", "Sentry", "Error tracking with environment-based config")
        Container(api_docs, "API Documentation", "DRF Spectacular", "OpenAPI/Swagger interactive docs")
    }</p>

<p>Rel(users, api_gateway, "Uses", "HTTPS")
    Rel(iot_devices, api_gateway, "Sends data", "HTTP/MQTT")</p>

<p>Rel(api_gateway, web_api, "Routes to", "HTTP")
    Rel(api_gateway, auth_service, "Auth requests", "HTTP")</p>

<p>Rel(web_api, auth_service, "Validates tokens", "HTTP")
    Rel(web_api, primary_db, "Reads/Writes", "MySQL Protocol")
    Rel(web_api, cache_db, "Caches data", "Redis Protocol")
    Rel(web_api, task_workers, "Queues tasks", "Redis")
    Rel(web_api, file_storage, "Stores files", "File System/S3")</p>

<p>Rel(task_workers, primary_db, "Reads/Writes", "MySQL Protocol")
    Rel(task_workers, cache_db, "Gets tasks", "Redis Protocol")
    Rel(task_workers, external_services, "Sends notifications", "SMTP/HTTP")</p>

<p>Rel(scheduler, task_workers, "Schedules tasks", "Redis")</p>

<p>Rel(web_api, monitoring, "Sends errors", "HTTPS")
    Rel(task_workers, monitoring, "Sends errors", "HTTPS")</p>

<p>UpdateElementStyle(web_api, $fontColor="white", $bgColor="blue", $borderColor="navy")
    UpdateElementStyle(task_workers, $fontColor="white", $bgColor="green", $borderColor="darkgreen")
</div></p>

<h3>Key Containers:</h3>

<ul><li>  <strong>Web API</strong>: Django 2.2.15 + DRF with 20+ business modules</li><li>  <strong>Authentication Service</strong>: JWT-based stateless authentication</li><li>  <strong>Background Workers</strong>: Celery 4.2.2 for alarm processing and report generation</li><li>  <strong>Task Scheduler</strong>: Celery Beat for cron jobs and periodic maintenance</li><li>  <strong>Message Services</strong>: RabbitMQ for real-time device communication</li></ul>

<h3>Data Storage:</h3>

<ul><li>  <strong>Primary Database</strong>: MySQL with PyMySQL for all application and telemetry data</li><li>  <strong>Cache & Message Broker</strong>: Redis 3.2.1 for session cache and task queue</li><li>  <strong>File Storage</strong>: Custom storage for tank charts, maintenance images, reports</li></ul>

<h3>Supporting Services:</h3>

<ul><li>  <strong>Application Monitoring</strong>: Sentry for error tracking</li><li>  <strong>API Documentation</strong>: DRF Spectacular for OpenAPI/Swagger docs</li></ul>

<h2 class="page-break">Level 3: Component Diagram - Web API Container</h2>

<p>The component diagram breaks down the Web API container to show the internal structure and organization of the Django application.</p>

<h3>Core Infrastructure:</h3>

<ul><li>  <strong>API Router</strong>: Django URLs for routing requests to appropriate modules</li><li>  <strong>Middleware Stack</strong>: CORS, Auth, Logging, Rate Limiting</li></ul>

<h3>Authentication & User Management:</h3>

<ul><li>  <strong>Authentication Module</strong>: Login, logout, password reset</li><li>  <strong>User Management</strong>: User CRUD, roles, permissions</li><li>  <strong>Company Management</strong>: Multi-tenant company operations</li><li>  <strong>Site Management</strong>: Site configuration and monitoring</li></ul>

<h3>Business Domain Components:</h3>

<ul><li>  <strong>Tank Monitoring</strong>: ATG logs, alarms, calibration charts</li><li>  <strong>Smart Pump Management</strong>: Transactions, pricing, remote config</li><li>  <strong>Device Management</strong>: Device registration, firmware tracking</li><li>  <strong>Generator Monitoring</strong>: Runtime logs, maintenance tracking</li><li>  <strong>Solar Monitoring</strong>: Solar panel performance metrics</li><li>  <strong>Smart Counters</strong>: Counter machine logs and config</li><li>  <strong>Flowmeter Management</strong>: Flow measurement and calibration</li><li>  <strong>Hydrostatic Monitoring</strong>: Pressure-based tank monitoring</li><li>  <strong>TapNet Integration</strong>: Payment system integration</li><li>  <strong>Shift Management</strong>: Work shift tracking and reporting</li></ul>

<h3>Cross-Cutting Services:</h3>

<ul><li>  <strong>Reporting Engine</strong>: Consumption, sales, anomaly reports</li><li>  <strong>Analytics Engine</strong>: Business intelligence and KPIs</li><li>  <strong>Notification Manager</strong>: Email, SMS, push notifications</li><li>  <strong>Audit Logging</strong>: Comprehensive operation tracking</li></ul>

<h3>Utility Components:</h3>

<ul><li>  <strong>Public API</strong>: Token-based external integrations</li><li>  <strong>File Handler</strong>: Tank charts, maintenance images</li><li>  <strong>Data Validator</strong>: Input validation and PV flag calculation</li><li>  <strong>Price Management</strong>: Dynamic pricing and scheduling</li><li>  <strong>Tank Calibration</strong>: Volume calculation from height</li><li>  <strong>Version Management</strong>: API versioning and compatibility</li></ul>

<hr>

<h2 class="page-break">Level 4: Deployment Diagram</h2>

<p>The deployment diagram shows how the Smart Eye platform is deployed in a production environment with high availability and scalability considerations.</p>

<h3>Infrastructure Components:</h3>

<ul><li>  <strong>Cloud Provider</strong>: AWS/Digital Ocean/Azure</li><li>  <strong>Load Balancer</strong>: Nginx/HAProxy for SSL termination and load balancing</li><li>  <strong>Application Cluster</strong>: Docker Swarm/Kubernetes for container orchestration</li><li>  <strong>Database Cluster</strong>: MySQL with primary/replica setup for high availability</li><li>  <strong>Cache Cluster</strong>: Redis cluster for caching and message brokering</li><li>  <strong>File Storage</strong>: S3/MinIO for object storage</li><li>  <strong>Monitoring Stack</strong>: Sentry, Prometheus, Grafana for observability</li></ul>

<h3>Scalability Features:</h3>

<ul><li>  Multiple API instances behind load balancer</li><li>  Distributed Celery workers for background processing</li><li>  Database read replicas for reporting workloads</li><li>  Redis replication for cache availability</li><li>  Horizontal scaling capabilities</li></ul>

<hr>

<h2 class="page-break">Supporting Diagrams</h2>

<h3>Data Flow Diagram - Tank Monitoring</h3>

<p>Shows the complete flow of tank monitoring data from IoT devices through the API to dashboard updates.</p>

<div class="mermaid">
flowchart TD
    A[IoT Tank Device] -->|HTTP POST| B[API Gateway]
    B --> C[Tank Monitoring Module]
    C --> D{Validate Data}
    D -->|Valid| E[Store in AtgPrimaryLog]
    D -->|Invalid| F[Log Error & Reject]
    E --> G[Update LatestAtgLog]
    G --> H[Calculate Volume from Height]
    H --> I{Check Alarm Thresholds}
    I -->|Normal| J[Update Tank Status]
    I -->|Alarm| K[Queue Alarm Task]
    K --> L[Send Email/SMS Alert]
    J --> M[Cache Latest Reading]
    L --> M
    M --> N[Update Dashboard]

<p>style A fill:#e1f5fe
    style C fill:#fff3e0
    style K fill:#ffebee
    style L fill:#ffebee
</div></p>

<h4>Key Process Steps:</h4>

<ol><li>IoT Tank Device sends HTTP POST to API Gateway</li><li>Tank Monitoring Module validates incoming data</li><li>Valid data stored in AtgPrimaryLog and LatestAtgLog updated</li><li>Volume calculated from height using calibration charts</li><li>Alarm thresholds checked and alerts queued if needed</li><li>Latest readings cached and dashboard updated</li></ol>

<hr>

<h3>Sequence Diagram - Pump Transaction Flow</h3>

<p>Illustrates the real-time processing of pump transactions and asynchronous notification handling.</p>

<div class="mermaid">
sequenceDiagram
    participant PD as Pump Device
    participant API as Smart Eye API
    participant DB as Database
    participant CW as Celery Worker
    participant NS as Notification Service

<p>PD->>API: POST /smartpump/transaction_logger/
    API->>API: Validate transaction data
    API->>DB: Store transaction in TransactionData
    API->>API: Calculate totals and summaries
    API->>CW: Queue price update task (if needed)
    API-->>PD: 200 OK with confirmation</p>

<p>CW->>DB: Update pump configuration
    CW->>NS: Send price change notification
    NS->>NS: Send email to site managers</p>

<p>Note over API,DB: Real-time transaction processing
    Note over CW,NS: Asynchronous notifications
</div></p>

<h4>Transaction Flow:</h4>

<ol><li>Pump Device posts transaction data to API</li><li>API validates and stores transaction in database</li><li>Totals and summaries calculated</li><li>Price update tasks queued if needed</li><li>Confirmation sent back to pump device</li><li>Background workers handle configuration updates and notifications</li></ol>

<h3>Entity Relationship Overview</h3>

<p>Shows the core data model relationships in the Smart Eye API platform, including multi-tenant structure and device data flows.</p>

<div class="mermaid">
erDiagram
    COMPANIES ||--o{ SITES : owns
    COMPANIES ||--o{ USERS : employs
    COMPANIES ||--o{ TANKS : owns
    COMPANIES ||--o{ DEVICES : owns
    SITES ||--o{ DEVICES : contains
    SITES ||--o{ TANKS : contains
    SITES ||--o{ PUMPS : contains
    SITES ||--o{ EQUIPMENTS : contains
    SITES ||--o{ FLOWMETERS : contains
    DEVICES ||--o{ ATG_PRIMARY_LOG : generates
    TANKS ||--o{ LATEST_ATG_LOG : "has current state"
    TANKS ||--o{ TANK_GROUPS : "belongs to"
    PUMPS ||--o{ NOZZLES : contains
    PUMPS ||--o{ TRANSACTION_DATA : generates
    USERS }o--|| ROLES : "assigned to"
    USERS }o--o{ SITES : "has access to"
    ROLES ||--o{ PERMISSIONS : contains
    SITES ||--o{ HYDROSTATIC_LOGS : generates
    SITES ||--o{ COUNTER_MACHINE_LOGS : generates

<p>COMPANIES {
        int Company_id PK
        string Name
        string Country
        string State
        boolean Active
        boolean smarttank_access
        boolean smartpump_access
        boolean genhours_access
        boolean shift_management_access
        boolean analytics_access
        int Owner FK
    }</p>

<p>SITES {
        int Site_id PK
        int Company_id FK
        string Name
        string Site_type
        boolean Active
        boolean Communication_status
        boolean Email_Notification
        boolean smarttank_access
        boolean genhours_access
        boolean smartpump_access
        int Device_id FK
    }</p>

<p>TANKS {
        int Tank_id PK
        int Site_id FK
        int Company_id FK
        string Name
        int Capacity
        string Tank_controller
        int Tank_index
        string Control_mode
        boolean Status
        float Offset
        string UOM
        string Display_unit
    }</p>

<p>DEVICES {
        int Device_id PK
        int Site_id FK
        int Company_id FK
        string Device_unique_address
        boolean Active
        boolean Available
        datetime last_seen
        int transmit_interval
    }</p>

<p>ATG_PRIMARY_LOG {
        int local_id
        string device_address
        string pv
        string pv_flag
        int tank_index
        string read_at
        string controller_type
        string tank_id
        string transaction_id
    }</p>

<p>LATEST_ATG_LOG {
        int Tank_id PK
        string Tank_name
        float Volume
        string Height
        datetime last_updated_time
        int Site_id
        string siteName
        int Capacity
        string Product
        float Fill
        boolean Tank_Status
    }</p>

<p>PUMPS {
        int pump_id PK
        int site_id FK
        string pump_name
        string pump_address
        boolean status
        string pump_brand
    }</p>

<p>TRANSACTION_DATA {
        string UniqueID PK
        int pump_id FK
        int site_id FK
        float total_volume
        float total_amount
        datetime transaction_time
        string product_name
    }</p>

<p>HYDROSTATIC_LOGS {
        string unique_id PK
        string mac_address
        int tank_index
        datetime read_time
        float tank_height
        float tank_volume
        int tank_id
    }
</div></p>

<h4>Key Relationships:</h4>

<ul><li>  <strong>Companies</strong> own multiple <strong>Sites</strong>, <strong>Tanks</strong>, and <strong>Devices</strong></li><li>  <strong>Sites</strong> contain various equipment types (tanks, pumps, generators, etc.)</li><li>  <strong>Devices</strong> generate telemetry logs stored in specialized log tables</li><li>  <strong>Users</strong> have role-based access to specific sites within companies</li><li>  <strong>Tanks</strong> maintain both historical logs and latest state information</li></ul>

<hr>

<h3>Microservices Migration Plan</h3>

<p>Shows the planned evolution from the current monolithic Django application to a microservices architecture.</p>

<div class="mermaid">
graph TB
    subgraph "Current Monolith"
        M[Smart Eye Django App]
    end

<p>subgraph "Target Microservices Architecture"
        AG[API Gateway]</p>

<p>subgraph "Core Services"
            AS[Auth Service]
            US[User Service]
            CS[Company Service]
        end</p>

<p>subgraph "Domain Services"
            TS[Tank Service]
            PS[Pump Service]
            DS[Device Service]
            GS[Generator Service]
            SS[Solar Service]
        end</p>

<p>subgraph "Cross-Cutting Services"
            NS[Notification Service]
            RS[Reporting Service]
            ANS[Analytics Service]
        end</p>

<p>subgraph "Data Layer"
            CDB[(Core DB)]
            TDB[(Tank DB)]
            PDB[(Pump DB)]
            RDB[(Redis Cache)]
        end
    end</p>

<p>M -.->|Phase 1: Extract| AS
    M -.->|Phase 1: Extract| US
    M -.->|Phase 2: Extract| TS
    M -.->|Phase 2: Extract| PS
    M -.->|Phase 3: Extract| NS
    M -.->|Phase 3: Extract| RS</p>

<p>AG --> AS
    AG --> US
    AG --> CS
    AG --> TS
    AG --> PS
    AG --> DS
    AG --> GS
    AG --> SS</p>

<p>AS --> CDB
    US --> CDB
    CS --> CDB
    TS --> TDB
    PS --> PDB</p>

<p>NS --> RDB
    RS --> RDB
    ANS --> RDB</p>

<p>style M fill:#ffcdd2
    style AG fill:#e8f5e8
    style AS fill:#fff3e0
    style TS fill:#e3f2fd
    style PS fill:#f3e5f5
</div></p>

<h4>Migration Phases:</h4>

<ul><li>  <strong>Phase 1</strong>: Extract core services (Auth, User Management)</li><li>  <strong>Phase 2</strong>: Extract domain services (Tank, Pump monitoring)</li><li>  <strong>Phase 3</strong>: Extract cross-cutting services (Notifications, Reporting)</li></ul>

<h4>Target Architecture:</h4>

<ul><li>  <strong>API Gateway</strong>: Central entry point for all requests</li><li>  <strong>Core Services</strong>: Authentication, user management, company management</li><li>  <strong>Domain Services</strong>: Business-specific services for each equipment type</li><li>  <strong>Cross-Cutting Services</strong>: Shared services for notifications, reporting, analytics</li><li>  <strong>Data Layer</strong>: Distributed databases with service-specific data stores</li></ul>

<h2 class="page-break">Architecture Summary</h2>

<h3>Key Architectural Decisions</h3>

<ol><li><strong>Multi-Tenant Architecture</strong>: Company-based data isolation with role-based access control</li><li><strong>Modular Django Design</strong>: Separate apps for each business domain (tanks, pumps, devices, etc.)</li><li><strong>Asynchronous Processing</strong>: Celery for background tasks, alarms, and report generation</li><li><strong>Caching Strategy</strong>: Redis for session management, task queuing, and data caching</li><li><strong>Real-time Monitoring</strong>: WebSocket support for live dashboard updates</li><li><strong>Scalable Storage</strong>: Support for both local and cloud-based file storage</li><li><strong>Comprehensive Logging</strong>: Audit trails and error tracking with Sentry integration</li></ol>

<h3>Technology Stack Summary</h3>

<table>
<thead>
<tr>
<th>Layer</th>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>API Framework</strong></td>
<td>Django + DRF</td>
<td>2.2.15 + 3.10.0</td>
<td>REST API development</td>
</tr>
<tr>
<td><strong>Database</strong></td>
<td>MySQL + PyMySQL</td>
<td>8.0 + 2.0.3</td>
<td>Primary data storage</td>
</tr>
<tr>
<td><strong>Cache/Queue</strong></td>
<td>Redis</td>
<td>3.2.1</td>
<td>Caching and message broker</td>
</tr>
<tr>
<td><strong>Task Processing</strong></td>
<td>Celery + Celery Beat</td>
<td>4.2.2</td>
<td>Background tasks and scheduling</td>
</tr>
<tr>
<td><strong>Message Broker</strong></td>
<td>RabbitMQ</td>
<td>Latest</td>
<td>Real-time device communication</td>
</tr>
<tr>
<td><strong>Authentication</strong></td>
<td>SimpleJWT</td>
<td>4.4.0</td>
<td>Stateless JWT authentication</td>
</tr>
<tr>
<td><strong>Documentation</strong></td>
<td>DRF Spectacular</td>
<td>0.15.1</td>
<td>OpenAPI/Swagger documentation</td>
</tr>
<tr>
<td><strong>Monitoring</strong></td>
<td>Sentry SDK</td>
<td>0.12.3</td>
<td>Error tracking and performance monitoring</td>
</tr>
<tr>
<td><strong>File Storage</strong></td>
<td>Custom Storage</td>
<td>-</td>
<td>Local/cloud file management</td>
</tr>
<tr>
<td><strong>Load Balancing</strong></td>
<td>Nginx/Gunicorn</td>
<td>20.1.0</td>
<td>WSGI server and reverse proxy</td>
</tr>
<tr>
<td><strong>Notifications</strong></td>
<td>Twilio + FCM Django</td>
<td>6.0.0 + 1.0.6</td>
<td>SMS and push notifications</td>
</tr>
<tr>
<td><strong>Audit Logging</strong></td>
<td>Django AuditLog</td>
<td>0.4.7</td>
<td>Comprehensive operation tracking</td>
</tr>
<tr>
<td><strong>CORS</strong></td>
<td>Django CORS Headers</td>
<td>3.7.0</td>
<td>Cross-origin resource sharing</td>
</tr>
<tr>
<td><strong>Cron Jobs</strong></td>
<td>Django Crontab</td>
<td>0.7.1</td>
<td>Scheduled task management</td>
</tr>
</tbody>
</table>
<h3>Scalability Considerations</h3>

<ol><li><strong>Horizontal Scaling</strong>: Multiple API instances behind load balancer</li><li><strong>Database Optimization</strong>: Read replicas for reporting workloads</li><li><strong>Caching Strategy</strong>: Multi-level caching (Redis + application-level)</li><li><strong>Background Processing</strong>: Distributed Celery workers</li><li><strong>File Storage</strong>: Cloud-based object storage for scalability</li><li><strong>Monitoring</strong>: Comprehensive observability stack</li></ol>

<h3>Security Features</h3>

<ol><li><strong>Authentication</strong>: JWT-based stateless authentication</li><li><strong>Authorization</strong>: Role-based access control (RBAC)</li><li><strong>Data Isolation</strong>: Multi-tenant architecture with company-level isolation</li><li><strong>API Security</strong>: Rate limiting, CORS configuration, input validation</li><li><strong>Audit Logging</strong>: Comprehensive audit trails for all operations</li><li><strong>Secure Communication</strong>: HTTPS/TLS encryption for all communications</li></ol>

<h3>Future Roadmap</h3>

<ol><li><strong>Phase 1</strong>: Extract authentication and user management services</li><li><strong>Phase 2</strong>: Separate tank and pump monitoring into dedicated services</li><li><strong>Phase 3</strong>: Implement API gateway and service mesh</li><li><strong>Phase 4</strong>: Containerization and Kubernetes deployment</li><li><strong>Phase 5</strong>: Event-driven architecture with message streaming</li></ol>

<p>This architecture supports the current monolithic deployment while providing a clear path toward microservices migration as the platform scales.</p>

<hr>

<h2 class="page-break">API Module Structure</h2>

<h3>Core Business Modules</h3>

<table>
<thead>
<tr>
<th>Module</th>
<th>Endpoints</th>
<th>Primary Functions</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Authentication</strong></td>
<td><code>/login/</code>, <code>/logout/</code>, <code>/password_reset/</code></td>
<td>JWT authentication, password management</td>
</tr>
<tr>
<td><strong>Users</strong></td>
<td><code>/users/</code>, <code>/users/{id}/</code></td>
<td>User CRUD, role assignment</td>
</tr>
<tr>
<td><strong>Companies</strong></td>
<td><code>/companies/</code>, <code>/companies/{id}/</code></td>
<td>Multi-tenant company management</td>
</tr>
<tr>
<td><strong>Sites</strong></td>
<td><code>/sites/</code>, <code>/sites/{id}/</code></td>
<td>Site configuration and monitoring</td>
</tr>
<tr>
<td><strong>Devices</strong></td>
<td><code>/devices/</code>, <code>/devices/{id}/</code></td>
<td>IoT device registration and management</td>
</tr>
<tr>
<td><strong>Tanks</strong></td>
<td><code>/tanks/</code>, <code>/tanks/{id}/</code>, <code>/tanks/by_site/{id}</code></td>
<td>Tank configuration and monitoring</td>
</tr>
<tr>
<td><strong>Smart Pump</strong></td>
<td><code>/smartpump/transaction_logger/</code>, <code>/smartpump/remote_config/</code></td>
<td>Pump transactions and configuration</td>
</tr>
<tr>
<td><strong>Smart Logs</strong></td>
<td><code>/tankreading/latest/</code>, <code>/anomaly/</code></td>
<td>Real-time tank data and anomaly detection</td>
</tr>
<tr>
<td><strong>Reports</strong></td>
<td><code>/reports/consumption/</code>, <code>/reports/sales/</code></td>
<td>Business intelligence and reporting</td>
</tr>
<tr>
<td><strong>Analytics</strong></td>
<td><code>/analytics/dashboard/</code>, <code>/analytics/trends/</code></td>
<td>Data analysis and insights</td>
</tr>
</tbody>
</table>
<h3>Specialized Modules</h3>

<table>
<thead>
<tr>
<th>Module</th>
<th>Purpose</th>
<th>Key Features</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Hydrostatic</strong></td>
<td>Pressure-based tank monitoring</td>
<td>Alternative tank level measurement</td>
</tr>
<tr>
<td><strong>Smart Counters</strong></td>
<td>Counter machine monitoring</td>
<td>Retail counter integration</td>
</tr>
<tr>
<td><strong>Flowmeters</strong></td>
<td>Flow measurement</td>
<td>Liquid flow tracking and calibration</td>
</tr>
<tr>
<td><strong>Generator Hours</strong></td>
<td>Generator monitoring</td>
<td>Runtime tracking and maintenance</td>
</tr>
<tr>
<td><strong>Smart Solar</strong></td>
<td>Solar panel monitoring</td>
<td>Renewable energy performance</td>
</tr>
<tr>
<td><strong>TapNet</strong></td>
<td>Payment integration</td>
<td>Card-based payment systems</td>
</tr>
<tr>
<td><strong>Shift Management</strong></td>
<td>Work shift tracking</td>
<td>Employee shift monitoring</td>
</tr>
<tr>
<td><strong>Public Endpoints</strong></td>
<td>External integrations</td>
<td>Token-based API access</td>
</tr>
</tbody>
</table>
<h3>Data Processing Features</h3>

<ol><li><strong>PV Flag Calculation</strong>: Automatic volume change detection (1=no change, 2=decreasing, 3=increasing)</li><li><strong>Tank Calibration</strong>: Height-to-volume conversion using calibration charts</li><li><strong>Alarm Processing</strong>: Multi-level threshold monitoring (LL, L, H, HH)</li><li><strong>Price Management</strong>: Dynamic pricing with scheduled updates</li><li><strong>Anomaly Detection</strong>: Statistical analysis for unusual patterns</li><li><strong>Report Generation</strong>: Automated consumption and sales reporting</li><li><strong>Real-time Dashboards</strong>: Live data visualization and monitoring</li></ol>

<hr>

<h2 class="page-break">Conclusion</h2>

<p>The Smart Eye API represents a comprehensive IoT monitoring and management platform designed for industrial equipment. The architecture demonstrates:</p>

<ul><li>  <strong>Scalability</strong>: Designed to handle growing numbers of devices and users</li><li>  <strong>Reliability</strong>: Multi-level redundancy and error handling</li><li>  <strong>Security</strong>: Comprehensive security measures and audit trails</li><li>  <strong>Maintainability</strong>: Modular design with clear separation of concerns</li><li>  <strong>Future-Ready</strong>: Clear migration path to microservices architecture</li></ul>

<p>This documentation serves as a blueprint for understanding, maintaining, and evolving the Smart Eye API platform to meet future business requirements and technological advances.</p>

<hr>

<strong>Document Information:</strong>

<ul><li>  <strong>Generated</strong>: December 2024</li><li>  <strong>Version</strong>: 1.0</li><li>  <strong>Contact</strong>: Smart Eye Development Team</li><li>  <strong>Repository</strong>: smart-eye-api</li></ul>

</body>
</html>