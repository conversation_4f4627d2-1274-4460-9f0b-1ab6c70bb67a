# Smart Eye API

![IoT Monitoring](https://img.shields.io/badge/IoT-Monitoring-blue)
![Django](https://img.shields.io/badge/Django-2.2.15-green)
![DRF](https://img.shields.io/badge/Django_REST_Framework-3.12-red)

A comprehensive Django-based IoT monitoring and management platform for industrial systems including fuel tanks, pumps, generators, and other equipment.

## Table of Contents

-   [Core Architecture](#core-architecture)
-   [Project Structure](#project-structure)
-   [Key Features](#key-features)
-   [API Architecture](#api-architecture)
-   [Data Flow](#data-flow)
-   [Key Business Logic](#key-business-logic)
-   [Infrastructure & Deployment](#infrastructure--deployment)
-   [Future Plans](#future-architecture-plans)

## Core Architecture

### Technology Stack

-   **Backend**: Django 2.2.15 with Django REST Framework
-   **Database**: MySQL with PyMySQL connector
-   **Task Queue**: Celery with Redis
-   **Authentication**: JWT-based authentication using SimpleJWT
-   **API Documentation**: DRF Spectacular (OpenAPI/Swagger)
-   **Monitoring**: Sentry for error tracking
-   **File Storage**: Custom file storage implementation

## Project Structure

The project follows a modular Django app structure:

-   Main project in `atg_web/`
-   Business logic in `backend/` with specialized apps

### Core Business Modules

#### 1. Tank Monitoring System (SmartTank)

-   **Models**: Tanks, TankGroups, AtgPrimaryLog, LatestAtgLog
-   **Features**:
    -   Real-time tank volume and height monitoring
    -   Multiple controller support (MTC, TLS, HYD-2)
    -   Tank calibration charts and offset calculations
    -   Alarm notifications for critical levels
    -   Anomaly detection for unusual volume changes

#### 2. Smart Pump Management

-   **Models**: Pump, Nozzle, TransactionData, PumpBrand
-   **Features**:
    -   Fuel dispensing transaction logging
    -   Remote pump configuration
    -   Price change management
    -   Sales reporting and analytics
    -   Multiple pump brand support

#### 3. Device Management

-   **Models**: Devices, DeviceFirmwareVersion
-   **Features**:
    -   IoT device registration and management
    -   Firmware version tracking
    -   Device online/offline status monitoring
    -   Communication interval configuration

#### 4. Multi-Tenant Architecture

-   **Models**: Companies, Sites, User
-   **Features**:
    -   Company-based data isolation
    -   Site-specific device and tank management
    -   Role-based access control
    -   Hierarchical permissions system

#### 5. Additional Modules

-   Smart Solar: Solar panel monitoring
-   Smart FMS: Fleet Management System
-   Smart Counters: Counter machine monitoring
-   Generator Hours: Generator runtime tracking
-   Hydrostatic: Hydrostatic tank monitoring

## Key Features

### Authentication & Authorization

-   JWT-based authentication
-   Role-based permissions:
    -   Super Admin
    -   Product Admin
    -   Company Admin
    -   Custom Roles
-   Multi-company access control
-   Site-specific user permissions

### Real-Time Monitoring

-   Continuous tank level monitoring
-   Device communication status tracking
-   Automatic alarm notifications
-   Background task processing with Celery

### Reporting & Analytics

-   Daily consumption reports
-   Sales summary reports
-   Tank inventory reports
-   Anomaly detection reports
-   Email-based report delivery

## API Architecture

-   RESTful API design
-   Comprehensive API documentation with Swagger
-   Public endpoints for external integrations
-   Versioned API structure (`/api/v1/`)

## Data Flow

1. IoT Devices → Send data via HTTP/MQTT to API endpoints
2. API Endpoints → Process and validate incoming data
3. Celery Tasks → Handle background processing (alarms, calculations)
4. Database → Store processed data and maintain latest states
5. Notification System → Send alerts via email/push notifications
6. Frontend/Mobile Apps → Consume API for real-time dashboards

## Key Business Logic

### Tank Monitoring

-   PV Flag Calculation: Tracks volume changes (1=no change, 2=decreasing, 3=increasing)
-   Unit Conversion: Supports multiple units (Liters, Gallons, etc.)
-   Calibration: Custom tank calibration charts for accurate volume calculations
-   Alarm Levels: LL (Low-Low), L (Low), H (High), HH (High-High) thresholds

### Pump Management

-   Transaction Logging: Records all fuel dispensing transactions
-   Price Management: Dynamic pricing with scheduled price changes
-   Remote Configuration: Push configuration updates to pump controllers
-   Sales Analytics: Comprehensive sales reporting and analytics

## Infrastructure & Deployment

-   Environment-based Configuration: Separate settings for local, staging, production
-   Celery Background Tasks: Handles alarms, reports, and data processing
-   Cron Jobs: Scheduled tasks for maintenance and monitoring
-   File Management: Custom storage for calibration charts and company avatars
-   Database Optimization: Indexed queries for performance

## Future Architecture Plans

Planned migration to microservices architecture:

-   Service separation by domain (Tank, Pump, Solar, etc.)
-   API Gateway implementation
-   Container orchestration with Kubernetes
-   Message broker integration (RabbitMQ already in use)
-   Distributed tracing and monitoring
-   Enhanced observability and logging
-   CI/CD pipeline implementation
-   Cloud-based deployment (AWS, GCP, Azure)
